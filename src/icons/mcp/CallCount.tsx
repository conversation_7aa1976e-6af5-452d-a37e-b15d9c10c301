import type { SVGProps } from "react";
const SvgCallCount = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 16 16"
        {...props}
    >
        <path
            fill="#000"
            d="M.533 12.8h14.934a.533.533 0 0 0 0-1.067H.533a.533.533 0 1 0 0 1.066M3.2 11.732v-6.94c0-.285.243-.527.53-.527h8.54c.293 0 .53.237.53.529v6.938h1.067V4.794c0-.88-.714-1.595-1.597-1.595H3.73a1.6 1.6 0 0 0-1.596 1.595v6.939zm3.24-3.051a.428.428 0 0 0 .685-.11l.74-1.483.558 2.233a.426.426 0 0 0 .78.116l.975-1.627.452.452a.427.427 0 1 0 .602-.603l-.836-.838a.427.427 0 0 0-.668.082l-.726 1.21-.59-2.351a.427.427 0 0 0-.794-.088L6.626 7.66l-.42-.418a.43.43 0 0 0-.492-.08l-.839.418a.427.427 0 1 0 .382.763l.562-.281z"
        />
    </svg>
);
export default SvgCallCount;
